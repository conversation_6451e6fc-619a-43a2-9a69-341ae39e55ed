'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { Heart, MessageCircle, Send, MoreHorizontal } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import type { Comment } from './MemoryCapsule';

interface CommentSectionProps {
  comments: Comment[];
  lang: string;
  onAddComment: (content: string, parentId?: string) => void;
  onLikeComment: (commentId: string) => void;
  onReportComment?: (commentId: string) => void;
}

interface CommentItemProps {
  comment: Comment;
  lang: string;
  onReply: (content: string, parentId: string) => void;
  onLike: (commentId: string) => void;
  onReport?: (commentId: string) => void;
  isReply?: boolean;
}

const formatTimeAgo = (timestamp: string, t: any): string => {
  const now = new Date();
  const commentTime = new Date(timestamp);
  const diffInMinutes = Math.floor((now.getTime() - commentTime.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return t('square.comments.timeAgo.justNow');
  if (diffInMinutes < 60) return t('square.comments.timeAgo.minutesAgo', { minutes: diffInMinutes });
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return t('square.comments.timeAgo.hoursAgo', { hours: diffInHours });
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return t('square.comments.timeAgo.daysAgo', { days: diffInDays });
  
  const diffInWeeks = Math.floor(diffInDays / 7);
  return t('square.comments.timeAgo.weeksAgo', { weeks: diffInWeeks });
};

const CommentItem: React.FC<CommentItemProps> = ({
  comment,
  lang,
  onReply,
  onLike,
  onReport,
  isReply = false
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [showReplyInput, setShowReplyInput] = useState(false);
  const [replyContent, setReplyContent] = useState('');
  const [showReplies, setShowReplies] = useState(false);
  const [showMenu, setShowMenu] = useState(false);

  const handleReplySubmit = () => {
    if (replyContent.trim()) {
      onReply(replyContent.trim(), comment.id);
      setReplyContent('');
      setShowReplyInput(false);
      setShowReplies(true);
    }
  };

  const handleLike = () => {
    onLike(comment.id);
  };

  return (
    <div className={`${isReply ? 'ml-8 border-l-2 border-gray-100 dark:border-gray-800 pl-4' : ''}`}>
      <div className="flex gap-3 group">
        <Image
          src={comment.userAvatar}
          alt={comment.userName}
          width={32}
          height={32}
          className="w-8 h-8 rounded-full flex-shrink-0"
          unoptimized
        />
        
        <div className="flex-1 min-w-0">
          <div className="bg-gray-50 dark:bg-gray-800 rounded-2xl px-4 py-3">
            <div className="flex items-center gap-2 mb-1">
              <span className="font-medium text-sm text-gray-900 dark:text-white">
                {comment.userName}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {formatTimeAgo(comment.timestamp, t)}
              </span>
            </div>
            <p className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">
              {comment.content}
            </p>
          </div>
          
          <div className="flex items-center gap-4 mt-2 text-xs">
            <button
              onClick={handleLike}
              className={`flex items-center gap-1 hover:text-red-500 transition-colors ${
                comment.isLiked ? 'text-red-500' : 'text-gray-500 dark:text-gray-400'
              }`}
            >
              <Heart size={14} className={comment.isLiked ? 'fill-current' : ''} />
              <span>{comment.likes}</span>
            </button>
            
            <button
              onClick={() => setShowReplyInput(!showReplyInput)}
              className="text-gray-500 dark:text-gray-400 hover:text-blue-500 transition-colors"
            >
              {t('square.comments.reply')}
            </button>
            
            <div className="relative">
              <button
                onClick={() => setShowMenu(!showMenu)}
                className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors opacity-0 group-hover:opacity-100"
              >
                <MoreHorizontal size={14} />
              </button>
              
              {showMenu && (
                <div className="absolute top-full left-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-1 z-10">
                  <button
                    onClick={() => {
                      onReport?.(comment.id);
                      setShowMenu(false);
                    }}
                    className="w-full px-3 py-1 text-left text-xs text-red-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    {t('square.actions.report')}
                  </button>
                </div>
              )}
            </div>
          </div>
          
          {/* Reply Input */}
          {showReplyInput && (
            <div className="mt-3 flex gap-2">
              <Image
                src="https://i.pravatar.cc/32?u=current-user"
                alt="You"
                width={24}
                height={24}
                className="w-6 h-6 rounded-full flex-shrink-0"
                unoptimized
              />
              <div className="flex-1 flex gap-2">
                <input
                  type="text"
                  value={replyContent}
                  onChange={(e) => setReplyContent(e.target.value)}
                  placeholder={t('square.comments.replyTo', { name: comment.userName })}
                  className="flex-1 px-3 py-2 text-sm border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  onKeyPress={(e) => e.key === 'Enter' && handleReplySubmit()}
                />
                <button
                  onClick={handleReplySubmit}
                  disabled={!replyContent.trim()}
                  className="px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Send size={14} />
                </button>
              </div>
            </div>
          )}
          
          {/* Replies */}
          {comment.replies && comment.replies.length > 0 && (
            <div className="mt-3">
              {!showReplies ? (
                <button
                  onClick={() => setShowReplies(true)}
                  className="text-xs text-blue-500 hover:text-blue-600 transition-colors"
                >
                  {t('square.comments.showReplies', { count: comment.replies.length })}
                </button>
              ) : (
                <>
                  <button
                    onClick={() => setShowReplies(false)}
                    className="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors mb-3"
                  >
                    {t('square.comments.hideReplies')}
                  </button>
                  <div className="space-y-3">
                    {comment.replies.map((reply) => (
                      <CommentItem
                        key={reply.id}
                        comment={reply}
                        lang={lang}
                        onReply={onReply}
                        onLike={onLike}
                        onReport={onReport}
                        isReply={true}
                      />
                    ))}
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const CommentSection: React.FC<CommentSectionProps> = ({
  comments,
  lang,
  onAddComment,
  onLikeComment,
  onReportComment
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [newComment, setNewComment] = useState('');

  const handleSubmit = () => {
    if (newComment.trim()) {
      onAddComment(newComment.trim());
      setNewComment('');
    }
  };

  const handleReply = (content: string, parentId: string) => {
    onAddComment(content, parentId);
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-white mb-4">
        {t('square.comments.title')} ({comments.length})
      </h3>
      
      {/* Add Comment */}
      <div className="flex gap-3">
        <Image
          src="https://i.pravatar.cc/40?u=current-user"
          alt="You"
          width={40}
          height={40}
          className="w-10 h-10 rounded-full flex-shrink-0"
          unoptimized
        />
        <div className="flex-1 flex gap-2">
          <input
            type="text"
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder={t('square.comments.placeholder')}
            className="flex-1 px-4 py-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            onKeyPress={(e) => e.key === 'Enter' && handleSubmit()}
          />
          <button
            onClick={handleSubmit}
            disabled={!newComment.trim()}
            className="px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {t('square.comments.submit')}
          </button>
        </div>
      </div>
      
      {/* Comments List */}
      <div className="space-y-4">
        {comments.length === 0 ? (
          <div className="text-center py-8">
            <MessageCircle size={48} className="mx-auto text-gray-400 dark:text-gray-600 mb-3" />
            <p className="text-gray-500 dark:text-gray-400">
              {t('square.comments.noComments')}
            </p>
          </div>
        ) : (
          comments.map((comment) => (
            <CommentItem
              key={comment.id}
              comment={comment}
              lang={lang}
              onReply={handleReply}
              onLike={onLikeComment}
              onReport={onReportComment}
            />
          ))
        )}
      </div>
    </div>
  );
};

export default CommentSection;
