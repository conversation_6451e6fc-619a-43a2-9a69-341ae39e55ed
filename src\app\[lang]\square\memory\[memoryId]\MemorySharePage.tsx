'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { 
  ArrowLeft, 
  Heart, 
  MessageCircle, 
  Share2, 
  ExternalLink,
  User,
  Clock
} from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import type { ChatRecord } from '@/components/MemoryCapsule';
import ShareModal from '@/components/ShareModal';

interface MemorySharePageProps {
  memory: ChatRecord;
  lang: string;
}

const MemorySharePage: React.FC<MemorySharePageProps> = ({ memory, lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const [showShareModal, setShowShareModal] = useState(false);
  const [isLiked, setIsLiked] = useState(memory.isLiked || false);
  const [likeCount, setLikeCount] = useState(memory.stats.likes);

  const handleLike = () => {
    setIsLiked(!isLiked);
    setLikeCount(prev => isLiked ? prev - 1 : prev + 1);
  };

  const characterParticipant = memory.participants.find(p => p.id !== 'user');

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center gap-4">
            <Link
              href={`/${lang}/square`}
              className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <ArrowLeft size={20} className="text-gray-600 dark:text-gray-400" />
            </Link>
            <div className="flex-1">
              <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
                Shared Memory
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                From Alphane AI Memory Square
              </p>
            </div>
            <button
              onClick={() => setShowShareModal(true)}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Share2 size={16} />
              Share
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden">
          {/* Memory Header */}
          <div className="relative h-64 md:h-80">
            <Image
              src={memory.backgroundImage || 'https://picsum.photos/800/400'}
              alt={memory.title}
              fill
              className="object-cover"
              unoptimized
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
            
            {/* Memory Title Overlay */}
            <div className="absolute bottom-6 left-6 right-6">
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">
                {memory.title}
              </h2>
              <div className="flex items-center gap-4 text-white/80">
                <div className="flex items-center gap-2">
                  <Clock size={16} />
                  <span className="text-sm">{memory.createdAt}</span>
                </div>
                <div className="flex items-center gap-2">
                  <User size={16} />
                  <span className="text-sm">
                    {memory.participants.map(p => p.name).join(' & ')}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Participants */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-4">
              <div className="flex -space-x-2">
                {memory.participants.map((participant, index) => (
                  <Image
                    key={participant.id}
                    src={participant.avatar}
                    alt={participant.name}
                    width={48}
                    height={48}
                    className="w-12 h-12 rounded-full border-3 border-white dark:border-gray-800"
                    style={{ zIndex: memory.participants.length - index }}
                    unoptimized
                  />
                ))}
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 dark:text-white">
                  {memory.participants.map(p => p.name).join(' & ')}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Shared a beautiful memory moment
                </p>
              </div>
              {characterParticipant && (
                <Link
                  href={`/${lang}/character/${characterParticipant.id}`}
                  className="flex items-center gap-2 px-4 py-2 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-lg hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors"
                >
                  <ExternalLink size={16} />
                  <span className="text-sm font-medium">View Character</span>
                </Link>
              )}
            </div>
          </div>

          {/* Chat Messages */}
          <div className="p-6 space-y-4 max-h-96 overflow-y-auto">
            {memory.messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.side === 'right' ? 'flex-row-reverse' : ''} items-start gap-3`}
              >
                <Image
                  src={message.avatar}
                  alt={message.name}
                  width={40}
                  height={40}
                  className="w-10 h-10 rounded-full flex-shrink-0"
                  unoptimized
                />
                <div className={`max-w-[75%] ${message.side === 'right' ? 'text-right' : ''}`}>
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {message.name}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {message.timestamp}
                    </span>
                  </div>
                  <div
                    className={`rounded-2xl px-4 py-2 text-sm whitespace-pre-wrap ${
                      message.side === 'right'
                        ? 'bg-blue-500 text-white rounded-br-md'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white rounded-bl-md'
                    }`}
                  >
                    {message.text}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Actions */}
          <div className="p-6 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleLike}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                    isLiked 
                      ? 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400' 
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  <Heart size={18} className={isLiked ? 'fill-current' : ''} />
                  <span className="text-sm font-medium">{likeCount}</span>
                </button>
                
                <div className="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
                  <MessageCircle size={18} className="text-gray-600 dark:text-gray-400" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {memory.stats.comments}
                  </span>
                </div>
                
                <div className="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
                  <Share2 size={18} className="text-gray-600 dark:text-gray-400" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {memory.stats.shares}
                  </span>
                </div>
              </div>
              
              <Link
                href={`/${lang}/square`}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Explore More Memories
              </Link>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-8 text-center">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
              Create Your Own AI Memories
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Join Alphane AI and start building meaningful relationships with AI companions
            </p>
            <Link
              href={`/${lang}`}
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 transition-colors font-medium"
            >
              Get Started with Alphane AI
            </Link>
          </div>
        </div>
      </div>

      {/* Share Modal */}
      <ShareModal
        chatRecord={memory}
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        lang={lang}
      />
    </div>
  );
};

export default MemorySharePage;
