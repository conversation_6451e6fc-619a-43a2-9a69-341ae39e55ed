import { Metadata } from 'next';
import { generateChatRecords } from '@/lib/mock-data';
import MainAppLayout from '@/components/MainAppLayout';
import MemorySharePage from './MemorySharePage';

interface Props {
  params: Promise<{ lang: string; memoryId: string }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { lang, memoryId } = await params;
  
  // Find the memory record
  const chatRecords = generateChatRecords();
  const memory = chatRecords.find(record => record.id === memoryId);
  
  if (!memory) {
    return {
      title: 'Memory Not Found - Alphane AI',
      description: 'The requested memory could not be found.'
    };
  }

  const characterName = memory.participants.find(p => p.id !== 'user')?.name || '';
  const title = `${memory.title} - Alphane AI Memory`;
  const description = characterName 
    ? `Beautiful memory with ${characterName}: ${memory.title}. Experience AI companionship on Alphane AI.`
    : `${memory.title}. Experience AI companionship on Alphane AI.`;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      images: [
        {
          url: memory.backgroundImage || 'https://picsum.photos/1200/630',
          width: 1200,
          height: 630,
          alt: memory.title,
        },
      ],
      type: 'article',
      siteName: 'Alphane AI',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [memory.backgroundImage || 'https://picsum.photos/1200/630'],
    },
  };
}

export default async function MemorySharePageRoute({ params }: Props) {
  const { lang, memoryId } = await params;
  
  // Find the memory record
  const chatRecords = generateChatRecords();
  const memory = chatRecords.find(record => record.id === memoryId);
  
  if (!memory) {
    return (
      <MainAppLayout lang={lang}>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Memory Not Found
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              The memory you're looking for could not be found.
            </p>
            <a
              href={`/${lang}/square`}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Back to Memory Square
            </a>
          </div>
        </div>
      </MainAppLayout>
    );
  }

  return (
    <MainAppLayout lang={lang} title={memory.title}>
      <MemorySharePage memory={memory} lang={lang} />
    </MainAppLayout>
  );
}
