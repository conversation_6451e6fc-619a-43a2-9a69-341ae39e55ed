'use client';

import { useState } from 'react';
import { generateChatRecords } from '@/lib/mock-data';
import MainAppLayout from '@/components/MainAppLayout';
import MemoryCard from '@/components/MemoryCard';
import MemoryCapsule, { ChatRecord } from '@/components/MemoryCapsule';
import { useParams } from 'next/navigation';

export default function SquarePage() {
  const { lang } = useParams() as { lang: string };
  const [selectedChat, setSelectedChat] = useState<ChatRecord | null>(null);
  const [chatRecords] = useState(() => generateChatRecords());

  const handleChatClick = (chatRecord: ChatRecord) => {
    setSelectedChat(chatRecord);
  };

  const handleCloseDetail = () => {
    setSelectedChat(null);
  };

  const handleLike = () => {
    if (selectedChat) {
      // Handle like action
      console.log('Liked chat:', selectedChat.id);
    }
  };

  const handleComment = () => {
    if (selectedChat) {
      // Handle comment action
      console.log('Comment on chat:', selectedChat.id);
    }
  };

  const handleShare = () => {
    if (selectedChat) {
      // Handle share action
      console.log('Share chat:', selectedChat.id);
    }
  };

  return (
    <MainAppLayout lang={lang}>
      <div className="w-full bg-background text-foreground theme-transition p-1.5">
        <div className="columns-2 md:columns-3 lg:columns-3 xl:columns-4 2xl:columns-5 3xl:columns-5 gap-1.5 space-y-1.5">
          {chatRecords.map((chatRecord) => (
            <div key={chatRecord.id} className="break-inside-avoid">
              <MemoryCard
                chatRecord={chatRecord}
                onClick={() => handleChatClick(chatRecord)}
                aspectRatio={chatRecord.aspectRatio || 0.75}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Chat Detail Modal */}
      {selectedChat && (
        <MemoryCapsule
          chatRecord={selectedChat}
          isOpen={!!selectedChat}
          onClose={handleCloseDetail}
          onLike={handleLike}
          onComment={handleComment}
          onShare={handleShare}
          lang={lang}
        />
      )}
    </MainAppLayout>
  );
}