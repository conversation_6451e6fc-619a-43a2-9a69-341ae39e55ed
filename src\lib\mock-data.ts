export type SampleMoment = {
  storyTemplateId: string;
  character: Character;
  moment: {
    id: string;
    title: string;
    image: string;
  };
  stats: {
    likes: number;
    shares: number;
  };
  aspectRatio: number;
  publishedAt: string;
};

export type Character = {
  id: string;
  name: string;
  character_avatar: string;
  character_bg_image: string;
  creator_uid?: string;
  creator_name?: string;
};

// Import story types
import type { <PERSON>, StoryChapter, StoryChoice, StoryReward, StoryAchievement, StoryCharacter, StoryDetailData } from '@/types/story';
import type { Comment, RelatedContent } from '@/components/MemoryCapsule';

export type ManagedCharacter = {
  id: string;
  name: string;
  character_avatar: string;
  character_bg_image: string;
  description?: string;
  tags: string[];
  followers: number;
  heatScore: number;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
  lastEditedAt: string;
  isVerified: boolean;
  createdAt: string;
  status: 'active' | 'draft' | 'archived';
  gender: 'male' | 'female' | 'non-binary' | 'other';
  pov: 'first-person' | 'second-person' | 'third-person';
};

// --- Single Source of Truth for Characters ---
export const characters: Character[] = [
  {
    id: 'seraphina-ember',
    name: 'Seraphina',
    // Avatar: Close-up, alluring gaze
    character_avatar: 'https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?w=400&h=400&fit=crop&crop=face',
    // BG: Full body, elegant, mysterious
    character_bg_image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=800&fit=crop',
    creator_uid: 'creator-sophia-001',
    creator_name: 'Sophia'
  },
  {
    id: 'kaelen-shadow',
    name: 'Kaelen',
    // Avatar: Handsome, smirking
    character_avatar: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=400&h=400&fit=crop&crop=face',
    // BG: Full body, cool, leaning
    character_bg_image: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=500&h=800&fit=crop',
    creator_uid: 'creator-alex-002',
    creator_name: 'Alex'
  },
  {
    id: 'lyra-starlight',
    name: 'Lyra',
    // Avatar: Playful, looking back
    character_avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
    // BG: Dynamic, magical
    character_bg_image: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=500&h=800&fit=crop',
    creator_uid: 'creator-maya-003',
    creator_name: 'Maya'
  },
  {
    id: 'riven-nightblade',
    name: 'Riven',
    // Avatar: Sharp, intense gaze
    character_avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
    // BG: Action pose, warrior
    character_bg_image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=500&h=800&fit=crop',
    creator_uid: 'creator-david-004',
    creator_name: 'David'
  },
  {
    id: 'elara-moonfire',
    name: 'Elara',
    // Avatar: Gentle, smiling
    character_avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=400&fit=crop&crop=face',
    // BG: Serene, nature
    character_bg_image: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=500&h=800&fit=crop',
    creator_uid: 'creator-luna-005',
    creator_name: 'Luna'
  },
  {
    id: 'zane-cybernexus',
    name: 'Zane',
    // Avatar: Cyberpunk, cool
    character_avatar: 'https://images.unsplash.com/photo-1463453091185-61582044d556?w=400&h=400&fit=crop&crop=face',
    // BG: Futuristic city, techwear
    character_bg_image: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=500&h=800&fit=crop',
    creator_uid: 'creator-zara-006',
    creator_name: 'Zara'
  },
  {
    id: 'aiko',
    name: 'Aiko',
    // Avatar: Cheerful, vibrant anime-style girl
    character_avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=400&fit=crop&crop=face',
    // BG: School campus, bright and cheerful
    character_bg_image: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=500&h=800&fit=crop',
    creator_uid: 'creator-sophia-001',
    creator_name: 'Sophia'
  },
  {
    id: '1',
    name: 'Aiko',
    // Avatar: Cheerful, vibrant anime-style girl from story
    character_avatar: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDGA25Gp9LBnpBDxFKA9W0BZe_c41Gc5qmhPosL747dUsN2XultAfwU6gdb4-DnhHjci54pQ9XB2d6HtJvrqsRwcFlrhov4xDqVjj1DnSYilFXjkfqo1DsMqDPymVI410-852MVC6pFYF4Cap9RtsKJ0b6WzOtcm5aWmCuasIKfkYpT-GcEOpF-8f4HLXF3oqcjSy6zcbzEPxxP1RkCL_9uMSPWq6DAj0aqueuhF-itY7BhBCGpmLiJPSNlERzzXH4bfRa8TvuQ0sNc',
    // BG: Same as story cover image for consistency
    character_bg_image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDGA25Gp9LBnpBDxFKA9W0BZe_c41Gc5qmhPosL747dUsN2XultAfwU6gdb4-DnhHjci54pQ9XB2d6HtJvrqsRwcFlrhov4xDqVjj1DnSYilFXjkfqo1DsMqDPymVI410-852MVC6pFYF4Cap9RtsKJ0b6WzOtcm5aWmCuasIKfkYpT-GcEOpF-8f4HLXF3oqcjSy6zcbzEPxxP1RkCL_9uMSPWq6DAj0aqueuhF-itY7BhBCGpmLiJPSNlERzzXH4bfRa8TvuQ0sNc',
    creator_uid: 'creator-sophia-001',
    creator_name: 'Sophia'
  },
  {
    id: 'nova-starweaver',
    name: 'Nova',
    // Avatar: Cosmic, ethereal beauty
    character_avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=400&fit=crop&crop=face',
    // BG: Starry night, cosmic theme
    character_bg_image: 'https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=500&h=800&fit=crop',
    creator_uid: 'creator-cosmic-007',
    creator_name: 'CosmicDreamer'
  },
  {
    id: 'kai-stormrider',
    name: 'Kai',
    // Avatar: Adventurous, wind-swept
    character_avatar: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=400&h=400&fit=crop&crop=face',
    // BG: Mountain peaks, stormy sky
    character_bg_image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=500&h=800&fit=crop',
    creator_uid: 'creator-storm-008',
    creator_name: 'StormChaser'
  },
  {
    id: 'aria-melodyheart',
    name: 'Aria',
    // Avatar: Musical, expressive
    character_avatar: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=400&h=400&fit=crop&crop=face',
    // BG: Concert hall, musical notes
    character_bg_image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=500&h=800&fit=crop',
    creator_uid: 'creator-melody-009',
    creator_name: 'MelodyMaker'
  },
  {
    id: 'phoenix-flameheart',
    name: 'Phoenix',
    // Avatar: Fiery, passionate
    character_avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=400&fit=crop&crop=face',
    // BG: Volcanic landscape, flames
    character_bg_image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=500&h=800&fit=crop',
    creator_uid: 'creator-flame-010',
    creator_name: 'FlameForge'
  },
  {
    id: 'sage-wisdomkeeper',
    name: 'Sage',
    // Avatar: Wise, contemplative
    character_avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
    // BG: Ancient library, scrolls
    character_bg_image: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=500&h=800&fit=crop',
    creator_uid: 'creator-wisdom-011',
    creator_name: 'WisdomSeeker'
  },
  {
    id: 'luna-moonwhisper',
    name: 'Luna',
    // Avatar: Mysterious, moonlit
    character_avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
    // BG: Moonlit forest, silver light
    character_bg_image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=800&fit=crop',
    creator_uid: 'creator-moon-012',
    creator_name: 'MoonDancer'
  },
  {
    id: 'atlas-ironwill',
    name: 'Atlas',
    // Avatar: Strong, determined
    character_avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=400&fit=crop&crop=face',
    // BG: Mountain fortress, steel and stone
    character_bg_image: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=500&h=800&fit=crop',
    creator_uid: 'creator-iron-013',
    creator_name: 'IronForge'
  },
  {
    id: 'iris-dreamweaver',
    name: 'Iris',
    // Avatar: Dreamy, artistic
    character_avatar: 'https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?w=400&h=400&fit=crop&crop=face',
    // BG: Surreal dreamscape, floating islands
    character_bg_image: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=500&h=800&fit=crop',
    creator_uid: 'creator-dream-014',
    creator_name: 'DreamCatcher'
  },
  {
    id: 'orion-starguard',
    name: 'Orion',
    // Avatar: Noble, starlit
    character_avatar: 'https://images.unsplash.com/photo-1463453091185-61582044d556?w=400&h=400&fit=crop&crop=face',
    // BG: Space station, stars
    character_bg_image: 'https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=500&h=800&fit=crop',
    creator_uid: 'creator-star-015',
    creator_name: 'StarGuardian'
  },
  {
    id: 'vera-shadowdancer',
    name: 'Vera',
    // Avatar: Elegant, mysterious
    character_avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=400&fit=crop&crop=face',
    // BG: Gothic cathedral, shadows
    character_bg_image: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=500&h=800&fit=crop',
    creator_uid: 'creator-shadow-016',
    creator_name: 'ShadowMaster'
  },
  {
    id: 'echo-voidwalker',
    name: 'Echo',
    // Avatar: Ethereal, otherworldly
    character_avatar: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=400&h=400&fit=crop&crop=face',
    // BG: Void space, dimensional rifts
    character_bg_image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=500&h=800&fit=crop',
    creator_uid: 'creator-void-017',
    creator_name: 'VoidWalker'
  }
];


const sampleTitles = [
  "They told me the lost city was a myth. They were wrong.",
  "The last transmission from Titan base was just... static.",
  "My reflection in the chrome started talking back to me.",
  "Found this glowing mushroom in the Whispering Woods.",
  "The antique clock I bought ticked backwards. And then, it chimed thirteen.",
  "AIs dream of electric sheep. I dream of saving them.",
  "The dragon agreed to a truce, but its eyes still hold a burning grudge.",
  "Woke up with a tattoo I don't remember getting. It seems to be a map.",
  "The stars aligned tonight, and something ancient stirred in the depths.",
  "My coffee cup started floating. Tuesday mornings are getting weird.",
  "The library book returned itself. The librarian wasn't surprised.",
  "Found a door in my basement that wasn't there yesterday.",
  "The AI assistant asked me for advice. I'm not sure who's helping whom.",
  "My shadow started walking ahead of me. I'm trying to keep up.",
  "The music box plays a song that doesn't exist yet.",
  "Received a letter from my future self. The handwriting matches.",
  "The mirror showed a room I've never been in. But I recognized the furniture.",
  "My phone got a call from a number that won't be invented for 50 years.",
  "The cat spoke perfect French today. We had a lovely conversation.",
  "Found footprints in the snow leading to nowhere. They were mine.",
  "The painting in the hallway changes when no one's looking.",
  "My dreams started leaving notes on my nightstand.",
  "The elevator went to a floor that doesn't exist on the building plans.",
  "My reflection waved goodbye before I did.",
  "The compass points to 'maybe' instead of north now."
];

const usedTitles = new Set<string>();

function getRandomUniqueTitle(): string {
  if (usedTitles.size >= sampleTitles.length) {
    usedTitles.clear(); // Reset if all titles are used
  }
  let title;
  do {
    title = sampleTitles[Math.floor(Math.random() * sampleTitles.length)];
  } while (usedTitles.has(title));
  usedTitles.add(title);
  return title;
}

// Generates portrait aspect ratios (width / height)
export function generateRandomAspectRatio() {
  const ratios = [5/8, 5/6];
  return ratios[Math.floor(Math.random()*ratios.length)];
}

export function generateAllSampleMoments(count: number = 8): SampleMoment[] {
  const allMoments: SampleMoment[] = [];

  for (let i = 0; i < count; i++) {
    const title = getRandomUniqueTitle();
    // Pick a random character from the centralized list
    const character = characters[i % characters.length];
    const aspectRatio = generateRandomAspectRatio();
    
    const moment: SampleMoment = {
      storyTemplateId: `story-template-${i}`,
      character: character,
      moment: {
        id: `moment-${i}`, // Simple, unique ID
        title: title,
        image: `https://picsum.photos/seed/${title.substring(0, 10)}/500/${Math.round(500 / aspectRatio)}`
      },
      stats: {
        likes: Math.floor(Math.random() * 5000) + 100,
        shares: Math.floor(Math.random() * 500) + 10,
      },
      aspectRatio: aspectRatio,
      publishedAt: `${Math.floor(Math.random() * 10) + 1}h ago`,
    };
    allMoments.push(moment);
  }

  return allMoments;
}

// Generate mock comments
const generateMockComments = (count: number): Comment[] => {
  const commentTexts = [
    "This is such a beautiful memory! 💕",
    "I love the way you captured this moment",
    "This reminds me of my own adventures",
    "So heartwarming to see this friendship",
    "The emotions in this conversation are so real",
    "This made me smile! Thank you for sharing",
    "What a wonderful story to preserve",
    "I can feel the connection between you two",
    "This is why I love this community",
    "Such genuine and touching moments"
  ];

  const userNames = [
    "Alice", "Bob", "Charlie", "Diana", "Eve", "Frank", "Grace", "Henry", "Iris", "Jack"
  ];

  return Array.from({ length: count }, (_, i) => {
    const hasReplies = Math.random() > 0.7; // 30% chance of having replies
    const replies = hasReplies ? Array.from({ length: Math.floor(Math.random() * 3) + 1 }, (_, j) => ({
      id: `reply-${i}-${j}`,
      userId: `user-${Math.floor(Math.random() * 10)}`,
      userName: userNames[Math.floor(Math.random() * userNames.length)],
      userAvatar: `https://i.pravatar.cc/32?u=user-${Math.floor(Math.random() * 10)}`,
      content: commentTexts[Math.floor(Math.random() * commentTexts.length)],
      timestamp: new Date(Date.now() - Math.random() * 86400000).toISOString(), // Random time in last 24h
      likes: Math.floor(Math.random() * 20),
      isLiked: Math.random() > 0.8,
      parentId: `comment-${i}`
    })) : undefined;

    return {
      id: `comment-${i}`,
      userId: `user-${Math.floor(Math.random() * 10)}`,
      userName: userNames[Math.floor(Math.random() * userNames.length)],
      userAvatar: `https://i.pravatar.cc/40?u=user-${Math.floor(Math.random() * 10)}`,
      content: commentTexts[Math.floor(Math.random() * commentTexts.length)],
      timestamp: new Date(Date.now() - Math.random() * 86400000).toISOString(), // Random time in last 24h
      likes: Math.floor(Math.random() * 50),
      isLiked: Math.random() > 0.8,
      replies
    };
  });
};

// Generate related content
const generateRelatedContent = (characterIndex: number): RelatedContent => {
  const character = characters[characterIndex % characters.length];
  const hasStory = Math.random() > 0.5; // 50% chance of having related story

  return {
    character: {
      id: character.id,
      name: character.name,
      avatar: character.character_avatar
    },
    story: hasStory ? {
      id: `story-${characterIndex}`,
      title: `Adventures with ${character.name}`,
      coverImage: `https://picsum.photos/seed/story-${characterIndex}/400/600`
    } : undefined
  };
};

// Mock chat records for Square page
export const generateChatRecords = () => {
  const memoryTitles = [
    "A magical evening under the starlit sky",
    "The moment we discovered the hidden garden",
    "Sharing secrets by the fireplace",
    "Dancing in the rain together",
    "Our first adventure in the enchanted forest",
    "Watching the sunrise from the mountain peak",
    "The day we found the ancient library",
    "Laughing until midnight in the cozy café",
    "Building sandcastles by the ocean",
    "Getting lost in the city and finding ourselves",
    "The night we saw shooting stars",
    "Cooking our favorite meal together",
    "Exploring the mysterious old castle",
    "The afternoon we spent in the art gallery",
    "Walking through the autumn leaves",
    "Our spontaneous road trip adventure",
    "The evening we learned to paint",
    "Discovering the secret rooftop garden",
    "The day we rescued a lost kitten",
    "Sharing stories under the cherry blossoms"
  ];

  const sampleMessages = [
    { text: "Hey! How's your day going?", side: 'left' as const },
    { text: "Pretty good! Just finished a great book", side: 'right' as const },
    { text: "Oh nice! What was it about?", side: 'left' as const },
    { text: "It's a sci-fi thriller about time travel", side: 'right' as const },
    { text: "That sounds amazing! I love sci-fi", side: 'left' as const },
    { text: "You should definitely read it!", side: 'right' as const },
    { text: "I'll add it to my reading list 📚", side: 'left' as const },
    { text: "Perfect! Let me know what you think", side: 'right' as const },
  ];

  return memoryTitles.map((title, index) => {
    const participants = [
      characters[index % characters.length],
      {
        id: 'user',
        name: 'You',
        avatar: 'https://i.pravatar.cc/40?u=user'
      }
    ];

    const messageCount = Math.floor(Math.random() * 6) + 3; // 3-8 messages
    const messages = Array.from({ length: messageCount }, (_, msgIndex) => ({
      id: msgIndex + 1,
      side: sampleMessages[msgIndex % sampleMessages.length].side,
      avatar: sampleMessages[msgIndex % sampleMessages.length].side === 'left'
        ? (participants[0] as Character).character_avatar || (participants[0] as any).avatar
        : (participants[1] as any).avatar,
      name: sampleMessages[msgIndex % sampleMessages.length].side === 'left'
        ? participants[0].name
        : participants[1].name,
      text: sampleMessages[msgIndex % sampleMessages.length].text,
      timestamp: `${Math.floor(Math.random() * 60) + 1}m ago`,
    }));

    // Use the same aspect ratio generation as moments for consistency
    const aspectRatio = generateRandomAspectRatio();
    const commentCount = Math.floor(Math.random() * 15) + 2; // 2-16 comments
    const comments = generateMockComments(commentCount);
    const relatedContent = generateRelatedContent(index);

    return {
      id: `memory-${index}`,
      title,
      backgroundImage: `https://picsum.photos/seed/${title.substring(0, 10)}/500/${Math.round(500 / aspectRatio)}`,
      participants: participants.map(p => ({
        id: p.id,
        name: p.name,
        avatar: (p as Character).character_avatar || (p as any).avatar
      })),
      messages,
      createdAt: `${Math.floor(Math.random() * 24) + 1}h ago`,
      aspectRatio,
      stats: {
        likes: Math.floor(Math.random() * 100) + 10,
        comments: commentCount,
        shares: Math.floor(Math.random() * 20) + 2,
      },
      comments,
      relatedContent,
      isLiked: Math.random() > 0.8 // 20% chance of being liked by current user
    };
  });
};

// Generate managed characters data for character management page
export const getManagedCharacters = (): ManagedCharacter[] => {
  const descriptions = [
    "A mysterious sorceress with ancient knowledge and a caring heart",
    "A skilled warrior with a strong sense of justice and honor",
    "A playful mage who loves adventure and making new friends",
    "A stoic guardian with unwavering loyalty and determination",
    "A gentle healer with deep wisdom and compassion",
    "A cyberpunk hacker with a rebellious spirit and tech expertise"
  ];

  const tagPool = [
    'Mysterious', 'Warrior', 'Playful', 'Stoic', 'Gentle', 'Cyberpunk',
    'Magical', 'Brave', 'Friendly', 'Loyal', 'Wise', 'Tech-savvy',
    'Ancient', 'Justice', 'Adventure', 'Guardian', 'Healer', 'Rebellious',
    'Caring', 'Honor', 'Cheerful', 'Determined', 'Compassionate', 'Hacker',
    'Sorceress', 'Knight', 'Mage', 'Protector', 'Sage', 'Futuristic'
  ];

  const characterTagSets = [
    ['Mysterious', 'Magical', 'Ancient'],
    ['Warrior', 'Brave', 'Honor'],
    ['Playful', 'Magical', 'Adventure'],
    ['Stoic', 'Guardian', 'Loyal'],
    ['Gentle', 'Healer', 'Wise'],
    ['Cyberpunk', 'Tech-savvy', 'Rebellious']
  ];

  const trends: Array<'up' | 'down' | 'stable'> = ['up', 'down', 'stable'];
  const genders: Array<'male' | 'female' | 'non-binary' | 'other'> = ['male', 'female', 'non-binary', 'other'];
  const povs: Array<'first-person' | 'second-person' | 'third-person'> = ['first-person', 'second-person', 'third-person'];

  return characters.map((character, index) => {
    const trend = trends[Math.floor(Math.random() * trends.length)];
    const trendPercentage = trend === 'stable'
      ? 0
      : (Math.floor(Math.random() * 30) + 1) * (trend === 'up' ? 1 : -1);

    const daysAgo = Math.floor(Math.random() * 30) + 1;
    const lastEditedAt = daysAgo === 1
      ? '1 day ago'
      : daysAgo < 7
        ? `${daysAgo} days ago`
        : `${Math.floor(daysAgo / 7)} week${Math.floor(daysAgo / 7) > 1 ? 's' : ''} ago`;

    return {
      ...character,
      description: descriptions[index % descriptions.length],
      tags: characterTagSets[index % characterTagSets.length],
      followers: Math.floor(Math.random() * 10000) + 500,
      heatScore: Math.floor(Math.random() * 5000) + 100,
      trend,
      trendPercentage,
      lastEditedAt,
      isVerified: Math.random() > 0.5, // 50% chance of being verified
      createdAt: `${Math.floor(Math.random() * 90) + 1} days ago`,
      status: 'active' as const,
      gender: genders[Math.floor(Math.random() * genders.length)],
      pov: povs[Math.floor(Math.random() * povs.length)]
    };
  });
};

// Story mock data
export const generateStoryData = (storyId: string): StoryDetailData => {
  const character = characters.find(c => c.id === 'aiko') || characters[0];

  const story: Story = {
    id: storyId,
    title: "Aiko的校园奇遇",
    description: "加入 Aiko 在 Alphane 学院的奇妙冒险！在这个充满活力的校园里，Aiko 将遇到形形色色的人物，解开神秘的谜题，并发现隐藏在日常学习生活之下的秘密。你的选择将决定故事的走向和 Aiko 的命运。",
    openingMessage: "新学期开始了，阳光明媚的早晨，你踏入了 Alphane 学院的大门。微风拂过脸颊，带来了阵阵花香和远处操场上传来的欢笑声。就在这时，一个充满活力的身影向你跑来，是 Aiko！'嘿！你也是新生吗？太巧了，我叫 Aiko，以后请多指教啦！'她笑着对你说，眼中闪烁着期待的光芒。",
    coverImage: "https://lh3.googleusercontent.com/aida-public/AB6AXuDGA25Gp9LBnpBDxFKA9W0BZe_c41Gc5qmhPosL747dUsN2XultAfwU6gdb4-DnhHjci54pQ9XB2d6HtJvrqsRwcFlrhov4xDqVjj1DnSYilFXjkfqo1DsMqDPymVI410-852MVC6pFYF4Cap9RtsKJ0b6WzOtcm5aWmCuasIKfkYpT-GcEOpF-8f4HLXF3oqcjSy6zcbzEPxxP1RkCL_9uMSPWq6DAj0aqueuhF-itY7BhBCGpmLiJPSNlERzzXH4bfRa8TvuQ0sNc",
    characterId: character.id,
    creatorUid: "storymaster",
    creatorName: "@StoryMaster",
    difficulty: 'normal',
    estimatedDuration: "30-45分钟",
    tags: ["校园", "青春", "奇幻", "友谊", "冒险"],
    stats: {
      plays: 1200,
      likes: 345,
      rating: 4.8,
      shares: 89
    },
    progress: {
      currentChapter: 3,
      totalChapters: 7,
      completedChapters: [1, 2]
    },
    status: 'active',
    createdAt: "2024-01-15",
    updatedAt: "2024-01-20"
  };

  const chapters: StoryChapter[] = [
    {
      id: 1,
      title: "初入校园",
      description: "与Aiko的第一次相遇",
      status: 'completed'
    },
    {
      id: 2,
      title: "食堂奇遇",
      description: "午餐时间的意外发现",
      status: 'completed'
    },
    {
      id: 3,
      title: "图书馆的秘密",
      description: "正在进行中...",
      status: 'current'
    },
    {
      id: 4,
      title: "社团招新",
      description: "需要完成前续章节",
      status: 'locked',
      unlockCondition: {
        type: 'previous_chapter',
        description: "需要完成前续章节"
      }
    },
    {
      id: 5,
      title: "校园祭准备",
      description: "需要达到羁绊等级4",
      status: 'locked',
      unlockCondition: {
        type: 'bond_level',
        value: 4,
        description: "需要达到羁绊等级4"
      }
    },
    {
      id: 6,
      title: "神秘事件",
      description: "解开校园的秘密",
      status: 'locked',
      unlockCondition: {
        type: 'previous_chapter',
        description: "需要完成前续章节"
      }
    },
    {
      id: 7,
      title: "完美结局",
      description: "故事的最终章",
      status: 'locked',
      unlockCondition: {
        type: 'previous_chapter',
        description: "需要完成前续章节"
      }
    }
  ];

  const choices: StoryChoice[] = [
    {
      id: "choice1",
      title: "🔍 深入调查神秘事件",
      description: "探索图书馆深处的秘密，可能发现隐藏剧情",
      icon: "🔍",
      consequences: "可能发现隐藏剧情"
    },
    {
      id: "choice2",
      title: "👥 寻求朋友帮助",
      description: "与Aiko一起行动，提升羁绊值",
      icon: "👥",
      consequences: "提升羁绊值"
    },
    {
      id: "choice3",
      title: "📚 专注学习任务",
      description: "按部就班完成作业，获得额外奖励",
      icon: "📚",
      consequences: "获得额外奖励"
    }
  ];

  const rewards: StoryReward[] = [
    {
      type: 'alphane_dust',
      amount: 120,
      name: '曦光微尘',
      icon: '🔥'
    },
    {
      type: 'bond_exp',
      amount: 50,
      name: '羁绊之露',
      icon: '💧'
    },
    {
      type: 'puzzle_piece',
      amount: 3,
      name: '拼图碎片',
      icon: '🧩'
    }
  ];

  const achievements: StoryAchievement[] = [
    {
      id: "achievement1",
      title: "学院新星",
      description: "完成校园奇遇故事线",
      icon: "🎓",
      rarity: 'gold',
      unlocked: false
    },
    {
      id: "achievement2",
      title: "真相探索者",
      description: "发现所有隐藏线索",
      icon: "🔍",
      rarity: 'silver',
      unlocked: false
    },
    {
      id: "achievement3",
      title: "最佳伙伴",
      description: "与Aiko达到最高羁绊",
      icon: "💕",
      rarity: 'platinum',
      unlocked: false
    }
  ];

  const storyCharacters: StoryCharacter[] = [
    {
      characterId: character.id,
      name: character.name,
      avatar: character.character_avatar,
      role: 'main',
      bondLevel: 5,
      unlocked: true
    },
    {
      characterId: "mysterious-senior",
      name: "神秘学长",
      avatar: "",
      role: 'supporting',
      unlocked: false
    }
  ];

  return {
    story,
    chapters,
    choices,
    rewards,
    achievements,
    characters: storyCharacters,
    unlockConditions: {
      bondLevel: 3,
      tutorialCompleted: true,
      hasMonthlyPass: false
    }
  };
};

// --- Managed Stories Data ---
export interface ManagedStory {
  id: string;
  title: string;
  description: string;
  characterId: string;
  characterName: string;
  characterAvatar: string;
  coverImage: string;
  status: 'draft' | 'published' | 'archived';
  plays: number;
  likes: number;
  rating: number;
  difficulty: 'easy' | 'normal' | 'hard';
  estimatedDuration: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  chapters: number;
  completionRate: number;
  isFeature: boolean;
}

export const getManagedStories = (): ManagedStory[] => {
  const managedCharacters = getManagedCharacters();
  
  return [
    {
      id: 'story-seraphina-001',
      title: 'Mystical Academy Chronicles',
      description: 'A magical adventure through the ancient halls of learning',
      characterId: 'seraphina-ember',
      characterName: 'Seraphina',
      characterAvatar: 'https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?w=400&h=400&fit=crop&crop=face',
      coverImage: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop',
      status: 'published',
      plays: 2847,
      likes: 1203,
      rating: 4.8,
      difficulty: 'normal',
      estimatedDuration: '45-60 min',
      tags: ['Magic', 'Adventure', 'Academy', 'Mystery'],
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-20T14:22:00Z',
      chapters: 12,
      completionRate: 87,
      isFeature: true
    },
    {
      id: 'story-kaelen-001',
      title: 'The Guardian\'s Quest',
      description: 'Join Kaelen on an epic journey to protect the realm',
      characterId: 'kaelen-shadow',
      characterName: 'Kaelen',
      characterAvatar: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=400&h=400&fit=crop&crop=face',
      coverImage: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop',
      status: 'published',
      plays: 1654,
      likes: 892,
      rating: 4.6,
      difficulty: 'hard',
      estimatedDuration: '60-90 min',
      tags: ['Action', 'Adventure', 'Fantasy', 'Hero'],
      createdAt: '2024-01-10T09:15:00Z',
      updatedAt: '2024-01-18T16:45:00Z',
      chapters: 15,
      completionRate: 73,
      isFeature: false
    },
    {
      id: 'story-lyra-001',
      title: 'Friendship & Magic',
      description: 'A heartwarming tale of friendship and magical discoveries',
      characterId: 'lyra-starlight',
      characterName: 'Lyra',
      characterAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
      coverImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop',
      status: 'draft',
      plays: 0,
      likes: 0,
      rating: 0,
      difficulty: 'easy',
      estimatedDuration: '30-45 min',
      tags: ['Friendship', 'Magic', 'Wholesome', 'Adventure'],
      createdAt: '2024-01-25T11:20:00Z',
      updatedAt: '2024-01-25T11:20:00Z',
      chapters: 8,
      completionRate: 0,
      isFeature: false
    },
    {
      id: 'story-riven-001',
      title: 'Silent Protector',
      description: 'A tale of duty, sacrifice, and silent strength',
      characterId: 'riven-nightblade',
      characterName: 'Riven',
      characterAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
      coverImage: 'https://images.unsplash.com/photo-1519452634681-4d2251882f32?w=800&h=600&fit=crop',
      status: 'published',
      plays: 987,
      likes: 456,
      rating: 4.4,
      difficulty: 'normal',
      estimatedDuration: '45-60 min',
      tags: ['Drama', 'Action', 'Loyalty', 'Sacrifice'],
      createdAt: '2024-01-08T14:30:00Z',
      updatedAt: '2024-01-15T10:12:00Z',
      chapters: 10,
      completionRate: 65,
      isFeature: false
    },
    {
      id: 'story-elara-001',
      title: 'Healer\'s Journey',
      description: 'Follow Elara as she discovers the true meaning of healing',
      characterId: 'elara-moonfire',
      characterName: 'Elara',
      characterAvatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=400&fit=crop&crop=face',
      coverImage: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop',
      status: 'published',
      plays: 1345,
      likes: 678,
      rating: 4.7,
      difficulty: 'easy',
      estimatedDuration: '30-45 min',
      tags: ['Healing', 'Wisdom', 'Compassion', 'Journey'],
      createdAt: '2024-01-12T16:45:00Z',
      updatedAt: '2024-01-19T09:30:00Z',
      chapters: 9,
      completionRate: 78,
      isFeature: true
    },
    {
      id: 'story-zane-001',
      title: 'Neon Nights',
      description: 'A cyberpunk thriller in the neon-lit streets of the future',
      characterId: 'zane-cybernexus',
      characterName: 'Zane',
      characterAvatar: 'https://images.unsplash.com/photo-1463453091185-61582044d556?w=400&h=400&fit=crop&crop=face',
      coverImage: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop',
      status: 'archived',
      plays: 543,
      likes: 234,
      rating: 4.2,
      difficulty: 'hard',
      estimatedDuration: '60-75 min',
      tags: ['Cyberpunk', 'Thriller', 'Technology', 'Mystery'],
      createdAt: '2024-01-05T13:20:00Z',
      updatedAt: '2024-01-22T11:15:00Z',
      chapters: 13,
      completionRate: 45,
      isFeature: false
    }
  ];
};