'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ExternalLink, User, BookO<PERSON>, Star, Heart } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import type { RelatedContent } from './MemoryCapsule';

interface CharacterStoryLinksProps {
  relatedContent: RelatedContent;
  lang: string;
  onClose?: () => void;
}

const CharacterStoryLinks: React.FC<CharacterStoryLinksProps> = ({
  relatedContent,
  lang,
  onClose
}) => {
  const { t } = useTranslation(lang, 'translation');

  if (!relatedContent.character && !relatedContent.story) {
    return null;
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
        <ExternalLink size={20} />
        {t('square.relatedContent.title')}
      </h3>
      
      {/* Character Link */}
      {relatedContent.character && (
        <Link
          href={`/${lang}/character/${relatedContent.character.id}`}
          onClick={onClose}
          className="block group"
        >
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-[1.02]">
            <div className="flex items-center gap-3">
              <div className="relative">
                <Image
                  src={relatedContent.character.avatar}
                  alt={relatedContent.character.name}
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full border-2 border-white/60"
                  unoptimized
                />
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                  <User size={12} className="text-white" />
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-semibold text-white truncate">
                    {relatedContent.character.name}
                  </h4>
                  <ExternalLink size={14} className="text-white/60 group-hover:text-white transition-colors" />
                </div>
                <p className="text-sm text-white/80">
                  {t('square.relatedContent.character')}
                </p>
              </div>
              
              <div className="flex items-center gap-1 text-white/60">
                <Heart size={16} />
                <span className="text-sm">
                  {Math.floor(Math.random() * 1000) + 100}
                </span>
              </div>
            </div>
            
            <div className="mt-3 flex items-center justify-between">
              <span className="text-xs text-white/60">
                {t('square.actions.viewCharacter')}
              </span>
              <div className="flex items-center gap-1">
                <Star size={12} className="text-yellow-400 fill-current" />
                <span className="text-xs text-white/80">
                  {(Math.random() * 2 + 3).toFixed(1)}
                </span>
              </div>
            </div>
          </div>
        </Link>
      )}
      
      {/* Story Link */}
      {relatedContent.story && (
        <Link
          href={`/${lang}/story/${relatedContent.story.id}`}
          onClick={onClose}
          className="block group"
        >
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-[1.02]">
            <div className="flex items-center gap-3">
              <div className="relative">
                <Image
                  src={relatedContent.story.coverImage}
                  alt={relatedContent.story.title}
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-lg object-cover border-2 border-white/60"
                  unoptimized
                />
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                  <BookOpen size={12} className="text-white" />
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-semibold text-white truncate">
                    {relatedContent.story.title}
                  </h4>
                  <ExternalLink size={14} className="text-white/60 group-hover:text-white transition-colors" />
                </div>
                <p className="text-sm text-white/80">
                  {t('square.relatedContent.story')}
                </p>
              </div>
              
              <div className="flex items-center gap-1 text-white/60">
                <BookOpen size={16} />
                <span className="text-sm">
                  {Math.floor(Math.random() * 20) + 5}
                </span>
              </div>
            </div>
            
            <div className="mt-3 flex items-center justify-between">
              <span className="text-xs text-white/60">
                {t('square.actions.viewStory')}
              </span>
              <div className="flex items-center gap-1">
                <Star size={12} className="text-yellow-400 fill-current" />
                <span className="text-xs text-white/80">
                  {(Math.random() * 2 + 3).toFixed(1)}
                </span>
              </div>
            </div>
          </div>
        </Link>
      )}
      
      {/* More Content Suggestions */}
      {relatedContent.character && (
        <div className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10">
          <h4 className="text-sm font-medium text-white/90 mb-3">
            {t('square.relatedContent.moreFromCharacter', { name: relatedContent.character.name })}
          </h4>
          
          <div className="grid grid-cols-2 gap-2">
            {/* Mock related memories */}
            {Array.from({ length: 4 }, (_, i) => (
              <div
                key={i}
                className="aspect-square rounded-lg bg-gradient-to-br from-purple-500/20 to-blue-500/20 border border-white/10 flex items-center justify-center group cursor-pointer hover:scale-105 transition-transform"
              >
                <div className="text-center">
                  <Heart size={16} className="mx-auto text-white/60 mb-1" />
                  <span className="text-xs text-white/60">
                    Memory {i + 1}
                  </span>
                </div>
              </div>
            ))}
          </div>
          
          <Link
            href={`/${lang}/character/${relatedContent.character.id}`}
            onClick={onClose}
            className="block mt-3 text-center text-sm text-blue-400 hover:text-blue-300 transition-colors"
          >
            {t('square.relatedContent.exploreStory')} →
          </Link>
        </div>
      )}
    </div>
  );
};

export default CharacterStoryLinks;
