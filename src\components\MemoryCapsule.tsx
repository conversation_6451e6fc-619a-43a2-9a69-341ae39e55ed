'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { X, Heart, MessageCircle, Share2, MoreHorizontal, ExternalLink } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import CommentSection from './CommentSection';
import CharacterStoryLinks from './CharacterStoryLinks';
import ShareModal from './ShareModal';

export interface ChatMessage {
  id: number;
  side: 'left' | 'right';
  avatar: string;
  name: string;
  text: string;
  timestamp: string;
  type?: 'text' | 'image' | 'emoji';
}

export interface Comment {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  content: string;
  timestamp: string;
  likes: number;
  isLiked?: boolean;
  replies?: Comment[];
  parentId?: string;
}

export interface RelatedContent {
  character?: {
    id: string;
    name: string;
    avatar: string;
  };
  story?: {
    id: string;
    title: string;
    coverImage: string;
  };
}

export interface ChatRecord {
  id: string;
  title: string;
  backgroundImage?: string;
  participants: {
    id: string;
    name: string;
    avatar: string;
  }[];
  messages: ChatMessage[];
  createdAt: string;
  aspectRatio?: number;
  stats: {
    likes: number;
    comments: number;
    shares: number;
  };
  comments?: Comment[];
  relatedContent?: RelatedContent;
  isLiked?: boolean;
}

interface MemoryCapsuleProps {
  chatRecord: ChatRecord;
  isOpen: boolean;
  onClose: () => void;
  onLike?: () => void;
  onComment?: () => void;
  onShare?: () => void;
  lang: string;
}

const MemoryCapsule: React.FC<MemoryCapsuleProps> = ({
  chatRecord,
  isOpen,
  onClose,
  onLike,
  onComment,
  onShare,
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [activeTab, setActiveTab] = useState<'chat' | 'comments' | 'related'>('chat');
  const [isLiked, setIsLiked] = useState(chatRecord.isLiked || false);
  const [likeCount, setLikeCount] = useState(chatRecord.stats.likes);
  const [comments, setComments] = useState<Comment[]>(chatRecord.comments || []);
  const [showShareModal, setShowShareModal] = useState(false);
  const [shareCount, setShareCount] = useState(chatRecord.stats.shares);

  if (!isOpen) return null;

  const handleLike = () => {
    setIsLiked(!isLiked);
    setLikeCount(prev => isLiked ? prev - 1 : prev + 1);
    onLike?.();
  };

  const handleAddComment = (content: string, parentId?: string) => {
    const newComment: Comment = {
      id: `comment-${Date.now()}`,
      userId: 'current-user',
      userName: 'You',
      userAvatar: 'https://i.pravatar.cc/40?u=current-user',
      content,
      timestamp: new Date().toISOString(),
      likes: 0,
      isLiked: false,
      parentId
    };

    if (parentId) {
      // Add as reply
      setComments(prev => prev.map(comment => {
        if (comment.id === parentId) {
          return {
            ...comment,
            replies: [...(comment.replies || []), newComment]
          };
        }
        return comment;
      }));
    } else {
      // Add as new comment
      setComments(prev => [newComment, ...prev]);
    }

    onComment?.();
  };

  const handleLikeComment = (commentId: string) => {
    setComments(prev => prev.map(comment => {
      if (comment.id === commentId) {
        return {
          ...comment,
          isLiked: !comment.isLiked,
          likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1
        };
      }
      // Handle replies
      if (comment.replies) {
        return {
          ...comment,
          replies: comment.replies.map(reply => {
            if (reply.id === commentId) {
              return {
                ...reply,
                isLiked: !reply.isLiked,
                likes: reply.isLiked ? reply.likes - 1 : reply.likes + 1
              };
            }
            return reply;
          })
        };
      }
      return comment;
    }));
  };

  const handleReportComment = (commentId: string) => {
    // Handle comment reporting
    console.log('Report comment:', commentId);
  };

  const handleShare = () => {
    setShowShareModal(true);
    setShareCount(prev => prev + 1);
    onShare?.();
  };

  const tabs = [
    { id: 'chat' as const, label: 'Chat', icon: MessageCircle },
    { id: 'comments' as const, label: t('square.comments.title'), icon: MessageCircle },
    { id: 'related' as const, label: t('square.relatedContent.title'), icon: ExternalLink }
  ];

  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
      <div className="rounded-2xl w-full max-w-4xl max-h-[90vh] flex flex-col shadow-2xl overflow-hidden">
        {/* Background Image Container - covers entire area */}
        <div className="relative flex-1 flex flex-col">
          {/* Background Image */}
          <Image
            src={chatRecord.backgroundImage || 'https://picsum.photos/800/400'}
            alt={chatRecord.title}
            fill
            className="absolute top-0 left-0 w-full h-full object-cover"
            unoptimized
            onError={() => {}}
          />

          {/* Gradient overlay - stronger at bottom for content readability */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/95 via-black/30 to-transparent" />

          {/* Header with close button and tabs */}
          <div className="relative z-10 p-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="font-semibold text-xl text-white">
                {chatRecord.title}
              </h2>
              <button
                onClick={onClose}
                className="p-2 rounded-full bg-black/30 hover:bg-black/50 transition-colors backdrop-blur-sm"
              >
                <X size={20} className="text-white" />
              </button>
            </div>

            {/* Tab Navigation */}
            <div className="flex gap-1 bg-black/20 backdrop-blur-sm rounded-lg p-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-all ${
                      activeTab === tab.id
                        ? 'bg-white/20 text-white'
                        : 'text-white/70 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    <Icon size={16} />
                    {tab.label}
                    {tab.id === 'comments' && comments.length > 0 && (
                      <span className="bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                        {comments.length}
                      </span>
                    )}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Content Area */}
          <div className="relative z-10 flex-1 overflow-y-auto px-4">
            {activeTab === 'chat' && (
              <div className="space-y-4">
                {chatRecord.messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.side === 'right' ? 'flex-row-reverse' : ''} items-start gap-3`}
                  >
                    <Image
                      src={message.avatar}
                      alt={message.name}
                      width={40}
                      height={40}
                      className="w-10 h-10 rounded-full flex-shrink-0"
                    />
                    <div className={`max-w-[75%] ${message.side === 'right' ? 'text-right' : ''}`}>
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-sm font-medium text-white/90">
                          {message.name}
                        </span>
                        <span className="text-xs text-white/60">
                          {message.timestamp}
                        </span>
                      </div>
                      <div
                        className={`rounded-2xl px-4 py-2 text-sm whitespace-pre-wrap ${
                          message.side === 'right'
                            ? 'bg-blue-500 text-white rounded-br-md'
                            : 'bg-white/90 backdrop-blur-sm text-gray-900 rounded-bl-md'
                        }`}
                      >
                        {message.text}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'comments' && (
              <CommentSection
                comments={comments}
                lang={lang}
                onAddComment={handleAddComment}
                onLikeComment={handleLikeComment}
                onReportComment={handleReportComment}
              />
            )}

            {activeTab === 'related' && chatRecord.relatedContent && (
              <CharacterStoryLinks
                relatedContent={chatRecord.relatedContent}
                lang={lang}
                onClose={onClose}
              />
            )}
          </div>

          {/* Bottom overlay with participants info and actions */}
          <div className="relative z-10 p-4 space-y-4">
            {/* Participants info */}
            <div className="flex items-center gap-3">
              <div className="flex -space-x-2">
                {chatRecord.participants.map((participant, index) => (
                  <Image
                    key={participant.id}
                    src={participant.avatar}
                    alt={participant.name}
                    width={40}
                    height={40}
                    className="w-10 h-10 rounded-full border-2 border-white/60"
                    style={{ zIndex: chatRecord.participants.length - index }}
                    unoptimized
                    onError={() => {}}
                  />
                ))}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <p className="text-sm text-white/80">
                    {chatRecord.participants.map(p => p.name).join(' & ')}
                  </p>
                  <span className="text-xs text-white/60">•</span>
                  <span className="text-xs text-white/60">
                    {chatRecord.createdAt}
                  </span>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleLike}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors backdrop-blur-sm ${
                    isLiked
                      ? 'bg-red-500/20 text-red-400 border border-red-400/30'
                      : 'bg-black/20 hover:bg-black/40 text-white/90'
                  }`}
                >
                  <Heart size={18} className={isLiked ? 'fill-current' : ''} />
                  <span className="text-sm">
                    {likeCount}
                  </span>
                </button>
                <button
                  onClick={() => setActiveTab('comments')}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors backdrop-blur-sm ${
                    activeTab === 'comments'
                      ? 'bg-blue-500/20 text-blue-400 border border-blue-400/30'
                      : 'bg-black/20 hover:bg-black/40 text-white/90'
                  }`}
                >
                  <MessageCircle size={18} />
                  <span className="text-sm">
                    {comments.length}
                  </span>
                </button>
                <button
                  onClick={handleShare}
                  className="flex items-center gap-2 px-3 py-2 rounded-lg bg-black/20 hover:bg-black/40 transition-colors backdrop-blur-sm text-white/90"
                >
                  <Share2 size={18} />
                  <span className="text-sm">
                    {shareCount}
                  </span>
                </button>
              </div>
              <button className="p-2 rounded-lg bg-black/20 hover:bg-black/40 transition-colors backdrop-blur-sm">
                <MoreHorizontal size={18} className="text-white/90" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Share Modal */}
      <ShareModal
        chatRecord={chatRecord}
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        lang={lang}
      />
    </div>
  );
};

export default MemoryCapsule;
