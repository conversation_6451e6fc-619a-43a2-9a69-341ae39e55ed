'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { 
  X, 
  Copy, 
  Download, 
  Share2, 
  MessageCircle,
  ExternalLink,
  Check
} from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import type { ChatRecord } from './MemoryCapsule';
import toast from 'react-hot-toast';
import {
  generateShareUrl,
  generateShareText,
  getPlatformShareUrl,
  copyToClipboard,
  generateShareImage,
  downloadImage,
  updateShareStats
} from '@/lib/share-utils';

interface ShareModalProps {
  chatRecord: ChatRecord;
  isOpen: boolean;
  onClose: () => void;
  lang: string;
}

interface SocialPlatform {
  id: string;
  name: string;
  icon: string;
  color: string;
}

const socialPlatforms: SocialPlatform[] = [
  {
    id: 'twitter',
    name: 'Twitter',
    icon: '🐦',
    color: 'bg-blue-500 hover:bg-blue-600'
  },
  {
    id: 'facebook',
    name: 'Facebook',
    icon: '📘',
    color: 'bg-blue-600 hover:bg-blue-700'
  },
  {
    id: 'telegram',
    name: 'Telegram',
    icon: '✈️',
    color: 'bg-blue-400 hover:bg-blue-500'
  },
  {
    id: 'discord',
    name: 'Discord',
    icon: '🎮',
    color: 'bg-indigo-500 hover:bg-indigo-600'
  },
  {
    id: 'wechat',
    name: 'WeChat',
    icon: '💬',
    color: 'bg-green-500 hover:bg-green-600'
  },
  {
    id: 'weibo',
    name: 'Weibo',
    icon: '🔴',
    color: 'bg-red-500 hover:bg-red-600'
  }
];

const ShareModal: React.FC<ShareModalProps> = ({
  chatRecord,
  isOpen,
  onClose,
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [copied, setCopied] = useState(false);
  const [generating, setGenerating] = useState(false);

  if (!isOpen) return null;

  const getShareUrl = () => {
    return generateShareUrl(chatRecord.id, lang);
  };

  const getShareText = (platform?: string) => {
    const characterName = chatRecord.participants.find(p => p.id !== 'user')?.name || '';
    return generateShareText(chatRecord.title, characterName, platform);
  };

  const handleCopyLink = async () => {
    const shareUrl = getShareUrl();
    const success = await copyToClipboard(shareUrl);

    if (success) {
      setCopied(true);
      toast.success(t('square.share.copySuccess'));
      setTimeout(() => setCopied(false), 2000);

      // Update share stats
      updateShareStats(chatRecord.id, 'copy');
    } else {
      toast.error(t('square.share.copyError'));
    }
  };

  const handleSocialShare = (platform: SocialPlatform) => {
    const shareUrl = getShareUrl();
    const shareText = getShareText(platform.id);
    const platformUrl = getPlatformShareUrl(platform.id, shareUrl, shareText);

    window.open(platformUrl, '_blank', 'width=600,height=400');
    toast.success(t('square.share.shareSuccess'));

    // Update share stats
    updateShareStats(chatRecord.id, platform.id);
  };

  const handleGenerateImage = async () => {
    setGenerating(true);
    try {
      const characterName = chatRecord.participants.find(p => p.id !== 'user')?.name;
      const imageDataUrl = await generateShareImage(
        chatRecord.title,
        chatRecord.backgroundImage || '',
        characterName
      );

      if (imageDataUrl) {
        downloadImage(imageDataUrl, `memory-${chatRecord.id}.png`);
        toast.success(t('square.share.messages.imageGenerated'));

        // Update share stats
        updateShareStats(chatRecord.id, 'image');
      } else {
        throw new Error('Failed to generate image');
      }
    } catch (error) {
      toast.error(t('square.share.shareError'));
      console.error('Image generation failed:', error);
    } finally {
      setGenerating(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-900 rounded-2xl w-full max-w-md shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="relative p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {t('square.share.title')}
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {t('square.share.subtitle')}
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <X size={20} className="text-gray-500 dark:text-gray-400" />
            </button>
          </div>
        </div>

        {/* Memory Preview */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <Image
              src={chatRecord.backgroundImage || 'https://picsum.photos/60/60'}
              alt={chatRecord.title}
              width={60}
              height={60}
              className="w-15 h-15 rounded-lg object-cover"
              unoptimized
            />
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-gray-900 dark:text-white truncate">
                {chatRecord.title}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {chatRecord.participants.map(p => p.name).join(' & ')}
              </p>
              <div className="flex items-center gap-4 mt-1 text-xs text-gray-500 dark:text-gray-400">
                <span>{chatRecord.stats.likes} likes</span>
                <span>{chatRecord.stats.comments} comments</span>
              </div>
            </div>
          </div>
        </div>

        {/* Share Options */}
        <div className="p-6 space-y-4">
          {/* Copy Link */}
          <button
            onClick={handleCopyLink}
            className="w-full flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            {copied ? (
              <Check size={20} className="text-green-500" />
            ) : (
              <Copy size={20} className="text-gray-600 dark:text-gray-400" />
            )}
            <span className="font-medium text-gray-900 dark:text-white">
              {t('square.share.copyLink')}
            </span>
          </button>

          {/* Generate Image */}
          <button
            onClick={handleGenerateImage}
            disabled={generating}
            className="w-full flex items-center gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors disabled:opacity-50"
          >
            <Download size={20} className="text-blue-600 dark:text-blue-400" />
            <span className="font-medium text-blue-900 dark:text-blue-300">
              {generating ? 'Generating...' : t('square.share.generateImage')}
            </span>
          </button>

          {/* Social Platforms */}
          <div>
            <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
              {t('square.share.socialMedia')}
            </h3>
            <div className="grid grid-cols-2 gap-2">
              {socialPlatforms.map((platform) => (
                <button
                  key={platform.id}
                  onClick={() => handleSocialShare(platform)}
                  className={`flex items-center gap-2 p-3 rounded-lg text-white transition-colors ${platform.color}`}
                >
                  <span className="text-lg">{platform.icon}</span>
                  <span className="text-sm font-medium">{platform.name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShareModal;
