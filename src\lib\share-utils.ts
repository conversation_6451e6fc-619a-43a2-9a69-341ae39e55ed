/**
 * Share utilities for memory capsules
 */

export interface ShareStats {
  memoryId: string;
  shares: number;
  lastShared: string;
  platforms: {
    [platform: string]: number;
  };
}

// In a real app, this would be stored in a database
const shareStatsCache = new Map<string, ShareStats>();

/**
 * Get share statistics for a memory
 */
export const getShareStats = (memoryId: string): ShareStats => {
  if (!shareStatsCache.has(memoryId)) {
    shareStatsCache.set(memoryId, {
      memoryId,
      shares: 0,
      lastShared: '',
      platforms: {}
    });
  }
  return shareStatsCache.get(memoryId)!;
};

/**
 * Update share statistics when a memory is shared
 */
export const updateShareStats = (memoryId: string, platform?: string): ShareStats => {
  const stats = getShareStats(memoryId);
  
  stats.shares += 1;
  stats.lastShared = new Date().toISOString();
  
  if (platform) {
    stats.platforms[platform] = (stats.platforms[platform] || 0) + 1;
  }
  
  shareStatsCache.set(memoryId, stats);
  
  // In a real app, you would also update the database here
  // await updateMemoryShareStats(memoryId, stats);
  
  return stats;
};

/**
 * Generate share URL for a memory
 */
export const generateShareUrl = (memoryId: string, lang: string = 'en'): string => {
  if (typeof window !== 'undefined') {
    const baseUrl = window.location.origin;
    return `${baseUrl}/${lang}/square/memory/${memoryId}`;
  }
  return `https://alphane.ai/${lang}/square/memory/${memoryId}`;
};

/**
 * Generate share text for different platforms
 */
export const generateShareText = (
  title: string, 
  characterName?: string, 
  platform?: string
): string => {
  const baseText = characterName 
    ? `Beautiful memory with ${characterName}: ${title}`
    : title;
    
  const suffix = ' - Experience AI companionship on Alphane AI';
  
  switch (platform) {
    case 'twitter':
      // Twitter has character limits
      const maxLength = 280 - 30; // Reserve space for URL
      const text = baseText + suffix;
      return text.length > maxLength ? text.substring(0, maxLength - 3) + '...' : text;
      
    case 'facebook':
      return baseText + suffix;
      
    case 'telegram':
      return `🤖 ${baseText}\n\n${suffix}`;
      
    case 'discord':
      return `**${baseText}**\n${suffix}`;
      
    case 'wechat':
    case 'weibo':
      return `${baseText} ${suffix}`;
      
    default:
      return baseText + suffix;
  }
};

/**
 * Get platform-specific share URL
 */
export const getPlatformShareUrl = (
  platform: string,
  shareUrl: string,
  shareText: string
): string => {
  const encodedUrl = encodeURIComponent(shareUrl);
  const encodedText = encodeURIComponent(shareText);
  
  switch (platform) {
    case 'twitter':
      return `https://twitter.com/intent/tweet?text=${encodedText}&url=${encodedUrl}`;
      
    case 'facebook':
      return `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedText}`;
      
    case 'telegram':
      return `https://t.me/share/url?url=${encodedUrl}&text=${encodedText}`;
      
    case 'discord':
      // Discord doesn't have a direct share URL, so we'll copy to clipboard
      return `https://discord.com/channels/@me?message=${encodeURIComponent(`${shareText} ${shareUrl}`)}`;
      
    case 'wechat':
      // WeChat sharing typically requires their SDK, fallback to copying
      return `weixin://dl/chat?text=${encodeURIComponent(`${shareText} ${shareUrl}`)}`;
      
    case 'weibo':
      return `https://service.weibo.com/share/share.php?url=${encodedUrl}&title=${encodedText}`;
      
    case 'qq':
      return `https://connect.qq.com/widget/shareqq/index.html?url=${encodedUrl}&title=${encodedText}`;
      
    default:
      return shareUrl;
  }
};

/**
 * Copy text to clipboard
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const success = document.execCommand('copy');
      document.body.removeChild(textArea);
      return success;
    }
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
};

/**
 * Generate share image data URL (simplified version)
 */
export const generateShareImage = async (
  title: string,
  backgroundImage: string,
  characterName?: string
): Promise<string> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      resolve('');
      return;
    }
    
    canvas.width = 1200;
    canvas.height = 630;
    
    // Background gradient
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
    gradient.addColorStop(0, '#667eea');
    gradient.addColorStop(1, '#764ba2');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Title
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 48px Arial, sans-serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    // Wrap text if too long
    const maxWidth = canvas.width - 100;
    const words = title.split(' ');
    let line = '';
    let y = canvas.height / 2 - 50;
    
    for (let n = 0; n < words.length; n++) {
      const testLine = line + words[n] + ' ';
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;
      
      if (testWidth > maxWidth && n > 0) {
        ctx.fillText(line, canvas.width / 2, y);
        line = words[n] + ' ';
        y += 60;
      } else {
        line = testLine;
      }
    }
    ctx.fillText(line, canvas.width / 2, y);
    
    // Character name
    if (characterName) {
      ctx.font = '32px Arial, sans-serif';
      ctx.fillText(`with ${characterName}`, canvas.width / 2, y + 80);
    }
    
    // Branding
    ctx.font = '24px Arial, sans-serif';
    ctx.fillStyle = '#ffffff99';
    ctx.fillText('Alphane AI', canvas.width / 2, canvas.height - 50);
    
    resolve(canvas.toDataURL('image/png'));
  });
};

/**
 * Download image from data URL
 */
export const downloadImage = (dataUrl: string, filename: string): void => {
  const link = document.createElement('a');
  link.download = filename;
  link.href = dataUrl;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
